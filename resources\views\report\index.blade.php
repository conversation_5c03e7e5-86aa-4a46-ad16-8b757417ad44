@extends('layouts.app')

@section('content')
<style>
/* LEO REPORT MODULE MODERN DESIGN - COMPACT VERSION */
.leo-report-container {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.07);
    margin: 20px auto;
    max-width: 98vw;
    padding: 0 0 18px 0;
}
.leo-report-header {
    background: linear-gradient(90deg, #3a7bd5 0%, #00d2ff 100%);
    color: #fff;
    border-radius: 12px 12px 0 0;
    padding: 14px 18px 10px 18px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}
.leo-report-title {
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}
.leo-report-filters {
    display: flex;
    gap: 10px;
    align-items: center;
}
.leo-filter-group {
    position: relative;
    min-width: 120px;
}
.leo-filter-input, .leo-filter-select {
    width: 100%;
    padding: 5px 28px 5px 8px;
    border: none;
    border-radius: 6px;
    background: rgba(255,255,255,0.18);
    color: #fff;
    font-size: 0.95rem;
    outline: none;
    height: 32px;
    transition: background 0.2s;
}
.leo-filter-input:focus, .leo-filter-select:focus {
    background: rgba(255,255,255,0.32);
}
.leo-filter-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
    pointer-events: none;
    font-size: 1em;
}
.leo-filter-select {
    width: 100%;
    padding: 5px 28px 5px 8px;
    border: none;
    border-radius: 6px;
    background: #f4f8fb;
    color: #222;
    font-size: 0.95rem;
    outline: none;
    height: 32px;
    transition: background 0.2s;
    appearance: auto;
}
.leo-filter-select:focus {
    background: #e3f2fd;
}
@media (max-width: 900px) {
    .leo-report-header { flex-direction: column; align-items: flex-start; gap: 10px; }
    .leo-report-filters { width: 100%; gap: 6px; }
    .leo-filter-group { min-width: 0; width: 100%; }
}

/* Password Protection Styles */
.password-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.password-container {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 400px;
    text-align: center;
}

.password-container h2 {
    margin-top: 0;
    color: #3a7bd5;
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.password-input {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.password-button {
    background: linear-gradient(90deg, #3a7bd5 0%, #00d2ff 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.password-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

.password-error {
    color: #e74c3c;
    margin-top: 10px;
    font-size: 0.9rem;
    display: none;
}

.content-container {
    display: none;
}
</style>

<!-- Password Protection Overlay -->
<div id="passwordOverlay" class="password-overlay">
    <div class="password-container">
        <h2>Accès Protégé</h2>
        <p>Veuillez entrer le mot de passe pour accéder au rapport</p>
        <input type="password" id="passwordInput" class="password-input" placeholder="Mot de passe">
        <div id="passwordError" class="password-error">Mot de passe incorrect</div>
        <button id="submitPassword" class="password-button">Accéder</button>
    </div>
</div>

<!-- Content Container (Hidden by default) -->
<div id="contentContainer" class="content-container">
    <div class="leo-report-container">
        <div class="leo-report-header">
            <div class="leo-report-title">
                <span style="font-size:1.5em;">📄</span>
                Rapport des Colis Reportés
            </div>
            <div class="leo-report-filters">
                <div class="leo-filter-group">
                    <input type="date" class="leo-filter-input" id="dateFilter" onchange="filterData()">
                    <span class="leo-filter-icon">📅</span>
                </div>
                <div class="leo-filter-group">
                    <select class="leo-filter-select" id="bureauFilter" onchange="filterData()">
                        <option value="">Tous les bureaux</option>
                        @foreach($bureaux as $bureau)
                            <option value="{{ $bureau }}">{{ $bureau }}</option>
                        @endforeach
                    </select>
                    <span class="leo-filter-icon">🏢</span>
                </div>
                <div class="leo-filter-group">
                    <input type="text" class="leo-filter-input" id="searchFilter" placeholder="Rechercher par nom expéditeur/destinataire" onchange="filterData()">
                    <span class="leo-filter-icon">🔍</span>
                </div>
            </div>
        </div>
        <div style="padding: 0 18px;">
            <div id="tableContainer">
                @include('report.table')
            </div>
        </div>
    </div>
</div>

<script>
// Password protection script
document.addEventListener('DOMContentLoaded', function() {
    const correctPassword = "leo2025$";
    const passwordOverlay = document.getElementById('passwordOverlay');
    const contentContainer = document.getElementById('contentContainer');
    const passwordInput = document.getElementById('passwordInput');
    const submitButton = document.getElementById('submitPassword');
    const passwordError = document.getElementById('passwordError');

    // Check if authentication is still valid (less than 1 hour old)
    const lastAuthTime = localStorage.getItem('leoReportAuthTime');
    const currentTime = new Date().getTime();
    const oneHourInMs = 60 * 60 * 1000; // 1 hour in milliseconds

    // If authenticated within the last hour, show content
    if (lastAuthTime && (currentTime - parseInt(lastAuthTime)) < oneHourInMs) {
        passwordOverlay.style.display = 'none';
        contentContainer.style.display = 'block';
    } else {
        // Otherwise require password
        passwordOverlay.style.display = 'flex';
        contentContainer.style.display = 'none';
    }

    // Password submission handler
    submitButton.addEventListener('click', checkPassword);
    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            checkPassword();
        }
    });

    function checkPassword() {
        if (passwordInput.value === correctPassword) {
            // Store current time as authentication timestamp
            localStorage.setItem('leoReportAuthTime', new Date().getTime());

            // Hide overlay and show content
            passwordOverlay.style.display = 'none';
            contentContainer.style.display = 'block';

            // Clear password field
            passwordInput.value = '';
            passwordError.style.display = 'none';
        } else {
            // Show error message
            passwordError.style.display = 'block';
            passwordInput.value = '';

            // Shake effect on error
            passwordInput.classList.add('shake');
            setTimeout(() => {
                passwordInput.classList.remove('shake');
            }, 500);
        }
    }
});

function filterData() {
    const date = document.getElementById('dateFilter').value;
    const bureau = document.getElementById('bureauFilter').value;
    const search = document.getElementById('searchFilter').value;

    fetch(`/reporte/colis?date=${date}&bureau=${bureau}&search=${search}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('tableContainer').innerHTML = html;
            setTimeout(function() {
                if ($.fn.DataTable.isDataTable('#colisTable')) {
                    $('#colisTable').DataTable().destroy();
                }
                $('#colisTable').DataTable({
                    "pageLength": 10,
                    "order": [[0, "desc"]],
                    "language": {
                        "search": "Rechercher:",
                        "lengthMenu": "Afficher _MENU_ entrées",
                        "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées",
                        "infoEmpty": "Aucune entrée à afficher",
                        "infoFiltered": "(filtré à partir de _MAX_ entrées totales)"
                    },
                    "dom": '<"leo-search-box"f>rt<"leo-pagination"lip><"clear">',
                    "initComplete": function() {
                        if (typeof highlightRowsWithDates === 'function') {
                            highlightRowsWithDates();
                        }
                    }
                });
            }, 300);
        });
}
</script>
@endsection