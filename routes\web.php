<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|


Route::get('/', function () {
    return view('welcome');
});
*/

//Default Controller
use App\Http\Controllers\VirementController;
use App\Http\Controllers\VirementOperationController;
use App\Http\Controllers\VirementEffectueController;
use App\Http\Controllers\ReportController;
use Illuminate\Support\Facades\Route;

Route::get('/', 'HomeController@index');
Route::post('/home/<USER>', 'HomeController@submit');
Route::get('/home/<USER>/{any?}', 'HomeController@getSkin');


Route::get('dashboard/import', 'DashboardController@getImport');
/* Auth & Profile */
Route::get('user/profile','UserController@getProfile');
Route::get('user/theme','UserController@getTheme');
Route::get('user/login','UserController@getLogin');
Route::get('user/register','UserController@getRegister');
Route::get('user/logout','UserController@getLogout');
Route::get('user/reminder','UserController@getReminder');
Route::get('user/reset/{any?}','UserController@getReset');
Route::get('user/reminder','UserController@getReminder');
Route::get('user/activation','UserController@getActivation');
// Social Login
Route::get('user/socialize/{any?}','UserController@socialize');
Route::get('user/autosocialize/{any?}','UserController@autosocialize');
//
Route::post('user/signin','UserController@postSignin');
Route::post('user/login','UserController@postSigninMobile');
Route::post('user/signup','UserController@postSignupMobile');
Route::post('user/create','UserController@postCreate');
Route::post('user/saveprofile','UserController@postSaveprofile');
Route::post('user/savepassword','UserController@postSavepassword');
Route::post('user/doreset/{any?}','UserController@postDoreset');
Route::post('user/request','UserController@postRequest');

/* Posts & Blogs */
Route::get('posts','HomeController@posts');
Route::get('posts/category/{any}','HomeController@posts');
Route::get('posts/read/{any}','HomeController@read');
Route::post('posts/comment','HomeController@comment');
Route::get('posts/remove/{id?}/{id2?}/{id3?}','HomeController@remove');
// Start Routes for Notification
Route::resource('notification','NotificationController');
Route::get('home/load','HomeController@getLoad');
Route::get('home/lang/{any}','HomeController@getLang');

Route::get('/set_theme/{any}', 'HomeController@set_theme');

include('pages.php');


Route::resource('sximoapi','SximoapiController');
Route::resource('services/posts', 'Services\PostController');



// Routes for  all generated Module
include('module.php');
// Custom routes
$path = base_path().'/routes/custom/';
$lang = scandir($path);
foreach($lang as $value) {
	if($value === '.' || $value === '..') {continue;}
	include( 'custom/'. $value );

}
// End custom routes
Route::group(['middleware' => 'auth'], function () {
	Route::resource('dashboard','DashboardController');
});


Route::group(['namespace' => 'Sximo','middleware' => 'auth'], function () {
	// This is root for superadmin

		include('sximo.php');

});

Route::group(['namespace' => 'Core','middleware' => 'auth'], function () {

	include('core.php');

});

Route::get('/scanning', function () {
    return view('scanning/scanning');
});

Route::get('/notclosed', function () {
    return view('scanning/notclosed');
});

Route::get('/notclosed/note/{id}', function ($id) {
    return view('scanning/note',["id"=>$id]);
});


Route::get('/demandesnotclosed', function () {
    return view('scanning/demandesnotclosed');
});


Route::group(['middleware' => 'auth'], function () {

Route::post('clients/mobileapp','ClientsController@mobileapp');
});

Route::middleware(['auth'])->group(function () {
    Route::resource('encaissements', EncaissementController::class)->except(['show']);
    Route::get('livraisons/{id}/montant', 'EncaissementController@getMontant');
    Route::get('livreurs/{livreur}/total/{date}', 'EncaissementController@getLivreurTotal');
    Route::get('/virements', [VirementController::class, 'index'])->name('virements.index');
    Route::post('/virements/create-rib', [VirementController::class, 'createRib'])->name('virements.create-rib');
    Route::post('/virements/update-selected-ribs', [VirementController::class, 'updateSelectedRibs'])->name('virements.update-selected-ribs');
    Route::get('livreurs/{livreur}/deliveries', 'EncaissementController@getLivreurDeliveries');
    Route::get('/virement-operations/{date}', [VirementOperationController::class, 'getOperations'])->name('virement-operations.get');
    Route::get('/virement-operations/{date}/export-biat', [VirementOperationController::class, 'exportBiat'])->name('virement-operations.export-biat');
    Route::get('/virement-operations/{date}/export-txt', [VirementOperationController::class, 'exportTxt'])->name('virement-operations.export-txt');
    Route::get('/encaissements/used-livreurs', 'EncaissementController@getUsedLivreurs');
    Route::get('/encaissements/cr-counts/{date}', [App\Http\Controllers\EncaissementController::class, 'getCrCounts']);
    Route::post('/encaissements/cloturer', [App\Http\Controllers\EncaissementController::class, 'cloturer']);
    Route::get('/virement-effectue/operations', [VirementEffectueController::class, 'getOperationsToMark'])->name('virement-effectue.operations');
    Route::post('/virement-effectue/mark', [VirementEffectueController::class, 'markAsEffectue'])->name('virement-effectue.mark');
    Route::get('/virement-effectue/exported/{date?}', [VirementEffectueController::class, 'getExportedOperations'])->name('virement-effectue.exported');
});

Route::put('/ribs/{rib}', [App\Http\Controllers\RibController::class, 'update'])->name('ribs.update');

Route::get('/ribs/{rib}', [App\Http\Controllers\RibController::class, 'show'])->name('ribs.show');

Route::get('/encaissements/photos/{date}', [App\Http\Controllers\EncaissementController::class, 'getPhotos']);
Route::get('/encaissements/anomalies/{date}', [App\Http\Controllers\EncaissementController::class, 'getAnomalies']);

Route::middleware(['auth'])->group(function () {
    Route::resource('type-colis', TypeColisController::class);
    Route::post('/type-colis/change-group', [App\Http\Controllers\TypeColisController::class, 'changeGroup'])
        ->name('type-colis.change-group');
    Route::get('/type-colis/client-prices/{tel}', [App\Http\Controllers\TypeColisController::class, 'getClientPrices'])
        ->name('type-colis.client-prices');
    Route::post('/type-colis/save-client-price', [App\Http\Controllers\TypeColisController::class, 'saveClientPrice'])
        ->name('type-colis.save-client-price');
    Route::post('/type-colis/bulk-update-prices', [App\Http\Controllers\TypeColisController::class, 'bulkUpdatePrices']);
});

Route::get('/cartons/assign/{colis_id}', [App\Http\Controllers\CartonController::class, 'assign'])->name('cartons.assign');
Route::get('/apii/type-colis/group-prices', [App\Http\Controllers\CartonController::class, 'getGroupPrices']);
Route::post('/apii/cartons/store', [App\Http\Controllers\CartonController::class, 'store']);

Route::group(['middleware' => ['auth']], function () {
    Route::get('/newfactures', [App\Http\Controllers\FactureController::class, 'index'])->name('newfactures.index');
    Route::get('/newfactures/getData', [App\Http\Controllers\FactureController::class, 'getData'])->name('newfactures.getData');
    Route::post('/newfactures/store', [App\Http\Controllers\FactureController::class, 'store'])->name('newfactures.store');
});

Route::get('/credit', [App\Http\Controllers\CreditController::class, 'index'])->name('credit.index');
Route::post('/credit/fetch', [App\Http\Controllers\CreditController::class, 'fetch'])->name('credit.fetch');
Route::post('/credit/update-caisse', [App\Http\Controllers\CreditController::class, 'updateCaisse'])->name('credit.updateCaisse');

// Add these routes with your other credit routes
Route::get('/vipclients', [App\Http\Controllers\CreditController::class, 'getVipClients'])->name('clients.vip');
Route::post('/colis/assign-parent', [App\Http\Controllers\CreditController::class, 'assignParent'])->name('colis.assign.parent');

// Add this with your other credit routes
Route::post('/credit/reset-reported', [App\Http\Controllers\CreditController::class, 'resetReported'])->name('credit.resetReported');
Route::post('/credit/toggle-payment-mode/{colisId}', [App\Http\Controllers\CreditController::class, 'togglePaymentMode'])->name('credit.togglePaymentMode');

Route::get('/apii/type-colis/group-prices-web', [App\Http\Controllers\CartonController::class, 'getGroupPricesWeb']);

Route::get('/search-clients', [App\Http\Controllers\ColisController::class, 'searchClients']);

// Report Routes
Route::get('/reporte', 'ReportController@index');
Route::get('/reporte/colis', 'ReportController@getReportedColis');
Route::post('/report/update-status/{id}', [ReportController::class, 'updateStatus'])->name('report.update-status');
Route::post('/reporte/mark-delivered/{id}', [ReportController::class, 'markDelivered'])->name('report.mark-delivered');
Route::get('/reporte/get-colis-details/{id}', [ReportController::class, 'getColisDetails'])->name('report.get-colis-details');
Route::post('/reporte/create-retour/{id}', [ReportController::class, 'createRetour'])->name('report.create-retour');
Route::get('/report/get-locations/{id}', [ReportController::class, 'getLocations']);

Route::get('/imprimer', function () {
    return view('imprimer');
})->name('imprimer');
