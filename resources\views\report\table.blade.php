<style>
/* <PERSON><PERSON><PERSON> TABLE, PAGINATION, BUTTONS, MODAL - COMPACT VERSION */
.leo-table-wrap { width: 100%; overflow-x: auto; }
.leo-datatable {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.95rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    margin: 0;
}
.leo-datatable thead th {
    background: #f4f8fb;
    color: #2d3a4a;
    font-weight: 600;
    padding: 8px 6px;
    border-bottom: 1px solid #e3e8ee;
    text-align: left;
    white-space: nowrap;
    font-size: 0.98em;
}
.leo-datatable tbody td {
    padding: 6px 8px;
    border-bottom: 1px solid #f0f2f5;
    vertical-align: middle;
    background: #fff;
    font-size: 0.97em;
}
.leo-datatable tbody tr:nth-child(even) td {
    background: inherit;
}
.leo-datatable tbody tr:hover td {
    background: #eaf6ff;
}

/* Custom table row colors */
.leo-datatable tbody tr.table-danger td {
    background-color: #fff1f1 !important;
}

.leo-datatable tbody tr.table-danger:hover td {
    background-color: #ffe4e4 !important;
}

.leo-datatable tbody tr.table-success td {
    background-color: #f0fff4 !important;
}

.leo-datatable tbody tr.table-success:hover td {
    background-color: #e4ffe9 !important;
}

.leo-datatable tbody tr.table-secondary td {
    background-color: #f8fafc !important;
}

.leo-datatable tbody tr.table-secondary:hover td {
    background-color: #f1f5f9 !important;
}

.leo-status-badge {
    display: inline-block;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: 0.92em;
    font-weight: 500;
    background: #e3f2fd;
    color: #1976d2;
    border: none;
}
.leo-status-secondary {
    background: #f5f5f5;
    color: #616161;
}
.leo-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    padding: 3px 8px;
    border-radius: 6px;
    border: 1px solid #e3e8ee;
    background: #fff;
    color: #1976d2;
    font-weight: 500;
    font-size: 0.95em;
    cursor: pointer;
    margin: 0 1px;
    transition: background 0.15s, color 0.15s, border 0.15s;
}
.leo-action-btn-primary { border-color: #1976d2; color: #1976d2; }
.leo-action-btn-primary:hover { background: #1976d2; color: #fff; }
.leo-action-btn-success { border-color: #43a047; color: #43a047; }
.leo-action-btn-success:hover { background: #43a047; color: #fff; }
.leo-action-btn-danger {
    border-color: #e53935;
    color: #e53935;
    padding: 1px 6px !important;
    font-size: 0.88em !important;
}
.leo-action-btn-danger:hover { background: #e53935; color: #fff; }

/* Enhanced Pagination Styles */
.leo-pagination {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    background: #ffffff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin: 30px 0;
}

.dataTables_wrapper {
    width: 100%;
}

/* Length Menu Styling */
.dataTables_length {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    background: #f8fafc;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.dataTables_length label {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #475569;
    font-weight: 500;
}

.dataTables_length select {
    appearance: none;
    background: #ffffff url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234f46e5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e") no-repeat right 8px center;
    background-size: 15px;
    padding: 8px 35px 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    color: #4f46e5;
    font-weight: 600;
    cursor: pointer;
    min-width: 90px;
}

.dataTables_length select:hover {
    border-color: #4f46e5;
}

/* Info Text Styling */
.dataTables_info {
    color: #64748b;
    font-size: 0.95rem;
    padding: 8px 16px;
    background: #f8fafc;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* Pagination Buttons */
.dataTables_paginate {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.pagination {
    display: flex;
    gap: 6px;
    padding: 0;
    margin: 0;
    list-style: none;
}

.paginate_button {
    min-width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e2e8f0;
    background: #ffffff;
    color: #4f46e5;
    border-radius: 8px;
    padding: 0 12px;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.2s ease;
    cursor: pointer;
    text-decoration: none;
}

.leo-pagination .paginate_button.current {
    background: #4f46e5;
    color: #ffffff;
    border-color: #4f46e5;
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.leo-pagination .paginate_button:hover:not(.current) {
    background: #e0e7ff;
    border-color: #4f46e5;
    color: #4f46e5;
}

.leo-pagination .paginate_button.previous,
.leo-pagination .paginate_button.next {
    font-size: 1.1rem;
    padding: 0 15px;
}

.leo-pagination .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #e5e7eb;
    color: #9ca3af;
    pointer-events: none;
}

.leo-pagination .ellipsis {
    color: #6b7280;
    margin: 0 4px;
}

/* DataTables info and length menu styling */
.dataTables_info {
    color: #6b7280;
    font-size: 0.95rem;
    margin: 10px 0;
    text-align: center;
}

.dataTables_length {
    margin: 15px 0;
    text-align: center;
}

.dataTables_length select {
    padding: 6px 12px;
    border: 2px solid #e0e7ff;
    border-radius: 8px;
    background: #ffffff;
    color: #4f46e5;
    font-weight: 500;
    margin: 0 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dataTables_length select:hover {
    border-color: #4f46e5;
}

.dataTables_length select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* Search box enhancement */
.leo-search-box {
    margin: 15px 0;
    text-align: center;
}

.leo-search-box input[type="search"] {
    width: 250px;
    padding: 10px 16px;
    border: 2px solid #e0e7ff;
    border-radius: 8px;
    background: #ffffff;
    color: #1f2937;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.leo-search-box input[type="search"]:hover {
    border-color: #4f46e5;
}

.leo-search-box input[type="search"]:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.leo-search-box input[type="search"]::placeholder {
    color: #9ca3af;
}

/* Modal */
.leo-modal-bg {
    position: fixed; left: 0; top: 0; width: 100vw; height: 100vh;
    background: rgba(44,62,80,0.18);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
}
.leo-modal-bg.active { display: flex; }
.leo-modal {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.13);
    max-width: 340px;
    width: 96vw;
    padding: 0;
    overflow: hidden;
    animation: fadeIn 0.2s;
}
@keyframes fadeIn { from { opacity: 0; transform: translateY(20px);} to { opacity: 1; transform: none; } }
.leo-modal-header {
    background: #1976d2;
    color: #fff;
    padding: 10px 16px;
    font-size: 1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.leo-modal-body { padding: 12px; text-align: center; }
.leo-modal-footer { padding: 8px 16px; text-align: right; background: #f4f8fb; }
.leo-modal-close {
    background: none; border: none; color: #fff; font-size: 1.2em; cursor: pointer;
}

/* Photo Grid Styles */
.photo-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    padding: 10px;
}

.photo-item {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 10px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.photo-item img {
    width: 100%;
    height: 200px;
    object-fit: contain;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: zoom-in;
    transition: transform 0.3s ease;
}

.photo-item:hover img {
    transform: scale(1.05);
}

.photo-item.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 2000;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.photo-item.fullscreen img {
    max-width: 90vw;
    max-height: 90vh;
    width: auto;
    height: auto;
    object-fit: contain;
    margin: 0;
}

.photo-item.fullscreen .photo-timestamp {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 8px 16px;
    border-radius: 4px;
}

.photo-item.fullscreen .close-fullscreen {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 24px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.7);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-timestamp {
    color: #1976d2;
    font-size: 0.9em;
    font-weight: 500;
    margin-bottom: 5px;
}

.photo-timestamp.text-muted {
    color: #6c757d;
    font-style: italic;
}

.pagination > li > a, .pagination > li > span{
    border: 0px solid #DDDDDD;
}

/* Additional Pagination Styles */
.paginate_button a {
    color: inherit;
    text-decoration: none;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.paginate_button.active,.paginate_button.active > a,
.paginate_button.current {
    background: #4f46e5!important;
    color: #ffffff!important;
    border-color: #4f46e5!important;
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.paginate_button:hover:not(.active):not(.disabled),
.paginate_button:focus:not(.active):not(.disabled) {
    background: #eef2ff;
    border-color: #4f46e5;
    color: #4f46e5;
}

.paginate_button.previous,
.paginate_button.next {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #e2e8f0;
    color: #94a3b8;
    pointer-events: none;
}

/* Responsive Layout */
@media (min-width: 768px) {
    .leo-pagination {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 15px 25px;
    }

    .dataTables_wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
    }

    .dataTables_length,
    .dataTables_info {
        margin: 0;
    }
}

/* Animation for hover effects */
@keyframes buttonPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.paginate_button:not(.disabled):active {
    transform: scale(0.98);
}

/* Custom scrollbar for the table */
.leo-table-wrap::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.leo-table-wrap::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.leo-table-wrap::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.leo-table-wrap::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>
<div class="leo-table-wrap">
    <table class="leo-datatable" id="colisTable">
        <thead>
            <tr>
                <th>ID</th>
                <th>Date Ramassage</th>
                <th>Nom Expéditeur</th>
                <th>Nom Destinataire</th>
                <th>Date photo</th>
                <th>Livreur affecte</th>
                <th>Bureau</th>
                <th>Position</th>
                <th>Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>            @forelse($colis as $coli)
                <tr id="row-{{ $coli->id }}" class="
                    @if($coli->locations_count > 0)
                        @if($coli->hours_since_last_log > 24)
                            table-danger
                        @else
                            table-success
                        @endif
                    @else
                        @if($coli->hours_since_creation > 48)
                            table-danger
                        @else
                            table-secondary
                        @endif
                    @endif
                ">
                    <td>{{ $coli->id }}</td>
                    <td>{{ $coli->date }}</td>
                    <td>{{ $coli->nom_exp }}</td>
                    <td>{{ $coli->nom_dest }}</td>
                    <td>{{ $coli->statusUpdatedAt ? \Carbon\Carbon::parse($coli->statusUpdatedAt)->format('Y-m-d H:i') : 'N/A' }}</td>
                    <td>{{ $coli->livreur ?? 'N/A' }}</td>
                    <td>{{ $coli->bureau ?? '' }} {{ $coli->agent ?? '' }}</td>                    <td>
                        @if($coli->latitude && $coli->longitude)
                            <button type="button" class="leo-action-btn leo-action-btn-primary" onclick="showLocations('{{ $coli->id }}')">
                                <span style="font-size:1.1em;">📍</span> Voir positions ({{ $coli->locations_count }})
                            </button>
                        @else
                            N/A
                        @endif
                    </td>
                    <td>
                        <span class="leo-status-badge {{ $coli->status ? '' : 'leo-status-secondary' }}">
                            {{ $coli->status ?? 'N/A' }}
                        </span>
                    </td>
                    <td>
                        <button type="button" class="leo-action-btn leo-action-btn-primary" onclick="showImage('{{ $coli->id }}')">
                            👁️
                        </button>
                        <button type="button" class="leo-action-btn leo-action-btn-success" onclick="updateStatus('{{ $coli->id }}', 'Livré')">
                            ✔️ Livré
                        </button>
                        <button type="button" class="leo-action-btn leo-action-btn-danger" onclick="showRetourModal('{{ $coli->id }}')">
                            🔄 Retour
                        </button>
                        <!-- <button type="button" class="leo-action-btn leo-action-btn-danger" onclick="updateStatus('{{ $coli->id }}', 'Annuler')">
                            ❌ Annuler
                        </button> -->
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="11" style="text-align:center;padding:32px 0;">Aucun colis reporté trouvé</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>
<!-- Custom Modal -->
<div class="leo-modal-bg" id="leoImageModalBg">
    <div class="leo-modal" style="max-width: 90%; max-height: 90vh;">
        <div class="leo-modal-header">
            Photos du Colis
            <button class="leo-modal-close" onclick="closeImageModal()">&times;</button>
        </div>
        <div class="leo-modal-body">
            <div id="leoModalSpinner" style="display:none;text-align:center;padding:24px 0;">
                <span style="font-size:2em;">⏳</span>
            </div>
            <div id="photoContainer" style="max-height: 70vh; overflow-y: auto; padding: 15px;">
                <div class="photo-grid">
                    <!-- Photos will be inserted here -->
                </div>
            </div>
        </div>
        <div class="leo-modal-footer">
            <button class="leo-action-btn leo-action-btn-primary" onclick="closeImageModal()">Fermer</button>
        </div>
    </div>
</div>

<!-- Map Modal -->
<div class="leo-modal-bg" id="mapModalBg">
    <div class="leo-modal" style="max-width: 800px; max-height: 90vh;">
        <div class="leo-modal-header">
            Positions du Colis
            <button class="leo-modal-close" onclick="closeMapModal()">&times;</button>
        </div>
        <div class="leo-modal-body">
            <div id="mapSpinner" style="display:none;text-align:center;padding:24px 0;">
                <span style="font-size:2em;">⏳</span>
            </div>
            <div id="locationsList" style="height: 150px; overflow-y: auto; margin-bottom: 10px;"></div>
            <div id="map" style="height: 400px;"></div>
        </div>
        <div class="leo-modal-footer">
            <button class="leo-action-btn leo-action-btn-primary" onclick="closeMapModal()">Fermer</button>
        </div>
    </div>
</div>

<!-- Delivery Confirmation Modal -->
<div class="leo-modal-bg" id="deliveryModalBg">
    <div class="leo-modal" style="max-width: 400px;">
        <div class="leo-modal-header">
            Confirmer la Livraison
            <button class="leo-modal-close" onclick="closeDeliveryModal()">&times;</button>
        </div>
        <div class="leo-modal-body">
            <p style="margin-bottom: 15px; text-align: left;">
                <strong>Êtes-vous sûr de vouloir marquer ce colis comme livré ?</strong>
            </p>
            <div style="margin-bottom: 15px; text-align: left;">
                <label for="deliveryDate" style="display: block; margin-bottom: 5px; font-weight: 500;">
                    Date de livraison :
                </label>
                <input type="date" id="deliveryDate" class="form-control" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
        </div>
        <div class="leo-modal-footer">
            <button class="leo-action-btn leo-action-btn-primary" onclick="closeDeliveryModal()">Annuler</button>
            <button class="leo-action-btn leo-action-btn-success" onclick="confirmDelivery()">Confirmer</button>
        </div>
    </div>
</div>

<!-- Retour Confirmation Modal -->
<div class="leo-modal-bg" id="retourModalBg">
    <div class="leo-modal" style="max-width: 600px;">
        <div class="leo-modal-header">
            Confirmer le Retour
            <button class="leo-modal-close" onclick="closeRetourModal()">&times;</button>
        </div>
        <div class="leo-modal-body">
            <p style="margin-bottom: 20px; text-align: left; color: #dc2626; font-weight: 600;">
                ⚠️ Cette action va annuler le colis actuel et créer un nouveau colis de retour avec les détails inversés.
            </p>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div>
                    <h4 style="color: #dc2626; margin-bottom: 10px;">📦 Colis Actuel (sera annulé)</h4>
                    <div id="currentColisDetails" style="background: #fef2f2; padding: 15px; border-radius: 8px; border-left: 4px solid #dc2626;">
                        <!-- Details will be populated by JavaScript -->
                    </div>
                </div>

                <div>
                    <h4 style="color: #059669; margin-bottom: 10px;">🔄 Nouveau Colis de Retour</h4>
                    <div id="newRetourDetails" style="background: #f0fdf4; padding: 15px; border-radius: 8px; border-left: 4px solid #059669;">
                        <!-- Details will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b; margin-bottom: 15px;">
                <strong>📋 Résumé des changements :</strong>
                <ul style="margin: 10px 0 0 20px; color: #92400e;">
                    <li>Expéditeur ↔ Destinataire (noms et téléphones inversés)</li>
                    <li>Bureau ↔ Livreur affecté (inversés)</li>
                    <li>Colis actuel marqué comme "Annulé"</li>
                    <li>Nouveau colis créé pour le retour</li>
                </ul>
            </div>
        </div>
        <div class="leo-modal-footer">
            <button class="leo-action-btn leo-action-btn-primary" onclick="closeRetourModal()">Annuler</button>
            <button class="leo-action-btn leo-action-btn-danger" onclick="confirmRetour()">Confirmer le Retour</button>
        </div>
    </div>
</div>

<!-- Add Leaflet CSS in the header -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

<!-- Add Leaflet JS before your existing scripts -->
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

<script>
window.highlightRowsWithDates = function() {
    const rows = document.querySelectorAll('tr[id^="row-"]');
    const today = new Date(); today.setHours(0,0,0,0);
    const dateFilter = document.getElementById('dateFilter');
    const selectedDate = dateFilter ? new Date(dateFilter.value) : null;
    if (selectedDate) selectedDate.setHours(0,0,0,0);
    rows.forEach(row => {
        const dateCell = row.cells[6];
        if (dateCell && dateCell.textContent !== 'N/A') {
            const rowDate = new Date(dateCell.textContent); rowDate.setHours(0,0,0,0);
            if (selectedDate && rowDate.getTime() === selectedDate.getTime()) {
                row.style.backgroundColor = '#e6ffe6';
            } else if (rowDate.getTime() === today.getTime()) {
                row.style.backgroundColor = '#e6ffe6';
            } else {
                row.style.backgroundColor = '#fffbe6';
            }
        } else {
            row.style.backgroundColor = '#ffeaea';
        }
    });
};
let imageModalInstance;
async function showImage(id) {
    const photoContainer = document.querySelector('#photoContainer .photo-grid');
    const spinner = document.getElementById('leoModalSpinner');
    photoContainer.innerHTML = '';
    spinner.style.display = 'block';

    try {
        const response = await fetch(`/aapi9/getphotos.php?id=${id}`);
        const data = await response.json();

        spinner.style.display = 'none';
        const totalPhotos = data.photos ? data.photos.length : 0;
        document.querySelector('.leo-modal-header').innerHTML = `
            Photos du Colis (${totalPhotos})
            <button class="leo-modal-close" onclick="closeImageModal()">&times;</button>
        `;

        if (data.photos && data.photos.length > 0) {
            data.photos.forEach(photo => {
                const photoDiv = document.createElement('div');
                photoDiv.className = 'photo-item';

                const timestamp = new Date(photo.timestamp * 1000);
                const dateStr = timestamp.toLocaleString();

                photoDiv.innerHTML = `
                    <div class="photo-timestamp">${dateStr}</div>
                    <img src="/adminphoto/reporte/${photo.filename}"
                         alt="Photo du colis"
                         loading="lazy"
                         onerror="handleImageError('${id}')"
                         onclick="openPhotoViewer(this)">
                    <div class="close-fullscreen" onclick="closePhotoViewer(this)" style="display: none;">×</div>
                `;
                photoContainer.appendChild(photoDiv);
            });
        } else {
            handleImageError(id);
        }
    } catch (error) {
        console.error('Error fetching photos:', error);
        handleImageError(id);
    }

    document.getElementById('leoImageModalBg').classList.add('active');
}

function openPhotoViewer(img) {
    const photoItem = img.parentElement;
    photoItem.classList.add('fullscreen');
    photoItem.querySelector('.close-fullscreen').style.display = 'flex';
    document.body.style.overflow = 'hidden';

    // Add keyboard event listener for ESC key
    document.addEventListener('keydown', handlePhotoViewerKeydown);
}

function handlePhotoViewerKeydown(e) {
    if (e.key === 'Escape') {
        closePhotoViewer();
    }
}

function closePhotoViewer(closeBtn) {
    // Remove keyboard event listener
    document.removeEventListener('keydown', handlePhotoViewerKeydown);

    // If called from close button, use that element, otherwise find the active fullscreen
    const photoItem = closeBtn ? closeBtn.parentElement : document.querySelector('.photo-item.fullscreen');
    if (photoItem) {
        photoItem.classList.remove('fullscreen');
        photoItem.querySelector('.close-fullscreen').style.display = 'none';
        document.body.style.overflow = '';
    }
}

let map = null;
let mapInitializationInProgress = false;

async function showLocations(id) {
    // Prevent multiple simultaneous initializations
    if (mapInitializationInProgress) {
        return;
    }

    mapInitializationInProgress = true;

    const mapModal = document.getElementById('mapModalBg');
    const spinner = document.getElementById('mapSpinner');
    const locationsList = document.getElementById('locationsList');

    spinner.style.display = 'block';
    locationsList.innerHTML = '';

    // Clean up existing map
    if (map) {
        map.remove();
        map = null;
    }

    // Show modal first
    mapModal.classList.add('active');

    try {
        const response = await fetch(`/report/get-locations/${id}`);
        const data = await response.json();

        spinner.style.display = 'none';

        if (data.locations && data.locations.length > 0) {
            // Wait for modal to be fully rendered before initializing map
            setTimeout(() => {
                try {
                    // Initialize map with proper error handling
                    map = L.map('map').setView([data.locations[0].latitude, data.locations[0].longitude], 13);

                    // Primary tile layer - Google Satellite
                    const googleSatelliteLayer = L.tileLayer('https://mts1.google.com/vt/lyrs=y@186112443&hl=x-local&src=app&x={x}&y={y}&z={z}&s=Galile', {
                        attribution: '© Google',
                        maxZoom: 21,
                        errorTileUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0iIzMzMyIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE4IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+Satellite</dGV4dD48L3N2Zz4='
                    });

                    // Fallback tile layer - OpenStreetMap
                    const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© OpenStreetMap contributors',
                        maxZoom: 19,
                        errorTileUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+No Tile</dGV4dD48L3N2Zz4='
                    });

                    // Try to add primary layer (Google Satellite), fallback to OSM if it fails
                    try {
                        googleSatelliteLayer.addTo(map);
                    } catch (tileError) {
                        console.warn('Google Satellite tiles failed, using OpenStreetMap fallback:', tileError);
                        osmLayer.addTo(map);
                    }

                    // Force map to recalculate size
                    setTimeout(() => {
                        if (map) {
                            map.invalidateSize();
                        }
                    }, 100);

                    // Create markers for all locations at once
                    const markers = [];

                    // Add markers and populate list
                    let listHtml = '<div class="location-list">';

                    // Sort locations by date (newest first)
                    const sortedLocations = [...data.locations].sort((a, b) =>
                        new Date(b.createdAt) - new Date(a.createdAt)
                    );

                    sortedLocations.forEach((loc, index) => {
                        // Create custom icon with number
                        const markerIcon = L.divIcon({
                            className: 'custom-marker-icon',
                            html: `<div class="marker-number">${index + 1}</div>`,
                            iconSize: [30, 30],
                            iconAnchor: [15, 15]
                        });

                        // Create marker with custom popup and icon
                        const marker = L.marker([loc.latitude, loc.longitude], { icon: markerIcon })
                            .addTo(map)
                            .bindPopup(`
                                <strong>Livreur:</strong> ${loc.agent}<br>
                                <strong>Bureau:</strong> ${loc.bureau}<br>
                                <strong>Date:</strong> ${new Date(loc.createdAt).toLocaleString()}<br>
                                <strong>Status:</strong> ${loc.status}
                            `);

                        markers.push(marker);

                        // Add to list with click handler to focus on specific marker
                        const date = new Date(loc.createdAt);
                        const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();

                        listHtml += `
                            <div class="location-item" onclick="focusMarker(${loc.latitude}, ${loc.longitude})">
                                <strong>${index + 1}. ${loc.bureau}</strong> - ${loc.status}<br>
                                <small>${formattedDate}</small>
                            </div>
                        `;
                    });
                    listHtml += '</div>';
                    locationsList.innerHTML = listHtml;

                    // Create a polyline connecting all markers in chronological order
                    const chronologicalLocations = [...data.locations].sort((a, b) =>
                        new Date(a.createdAt) - new Date(b.createdAt)
                    );

                    const polylinePoints = chronologicalLocations.map(loc => [loc.latitude, loc.longitude]);

                    if (polylinePoints.length > 1) {
                        const polyline = L.polyline(polylinePoints, {
                            color: '#3a7bd5',
                            weight: 3,
                            opacity: 0.7,
                            dashArray: '10, 10',
                            lineJoin: 'round'
                        }).addTo(map);
                    }

                    // Fit bounds to show all markers
                    const bounds = data.locations.map(loc => [loc.latitude, loc.longitude]);
                    map.fitBounds(bounds);

                    // Update modal title to show number of positions
                    document.querySelector('#mapModalBg .leo-modal-header').innerHTML = `
                        Positions du Colis (${data.locations.length})
                        <button class="leo-modal-close" onclick="closeMapModal()">&times;</button>
                    `;

                } catch (mapError) {
                    console.error('Error initializing map:', mapError);
                    locationsList.innerHTML = '<div class="text-center text-danger">Erreur lors de l\'initialisation de la carte</div>';
                }
            }, 300); // Wait 300ms for modal to be fully rendered

        } else {
            locationsList.innerHTML = '<div class="text-center">Aucune position trouvée</div>';
        }
    } catch (error) {
        console.error('Error fetching locations:', error);
        spinner.style.display = 'none';
        locationsList.innerHTML = '<div class="text-center text-danger">Erreur lors du chargement des positions</div>';
    } finally {
        mapInitializationInProgress = false;
    }
}

function focusMarker(lat, lng) {
    if (map) {
        map.setView([lat, lng], 15);
    }
}

function closeMapModal() {
    document.getElementById('mapModalBg').classList.remove('active');

    // Clean up map resources
    if (map) {
        map.remove();
        map = null;
    }
}

function updatePhotoCount(id) {
    fetch(`/aapi9/getphotos.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            const button = document.querySelector(`button[onclick="showImage('${id}')"]`);
            if (button) {
                const count = data.photos ? data.photos.length : 0;
                button.innerHTML = `👁️ ${count > 0 ? `(${count})` : ''}`;
            }
        })
        .catch(error => console.error('Error updating photo count:', error));
}

function closeImageModal() {
    document.getElementById('leoImageModalBg').classList.remove('active');
    // Exit fullscreen if any image is in fullscreen mode
    closePhotoViewer();
}

function handleImageError(id) {
    const photoContainer = document.querySelector('#photoContainer .photo-grid');
    const spinner = document.getElementById('leoModalSpinner');
    spinner.style.display = 'none';

    if (!document.getElementById('leoModalError')) {
        const errorDiv = document.createElement('div');
        errorDiv.id = 'leoModalError';
        errorDiv.style.cssText = `
            color: #e53935;
            font-weight: bold;
            padding: 24px 0;
            text-align: center;
            grid-column: 1 / -1;
        `;
        errorDiv.innerText = 'Aucune photo trouvée.';
        photoContainer.appendChild(errorDiv);
    }

    const row = document.getElementById(`row-${id}`);
    if (row) row.style.backgroundColor = '#ffeaea';
}

$(document).ready(function() {
    $('#colisTable').DataTable({
        "pageLength": 10,
        "order": [[0, "desc"]],
        "language": {
            "search": "Rechercher:",
            "lengthMenu": "Afficher _MENU_ entrées",
            "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées",
            "infoEmpty": "Aucune entrée à afficher",
            "infoFiltered": "(filtré à partir de _MAX_ entrées totales)"
        },
        "dom": '<"leo-search-box"f>rt<"leo-pagination"lip><"clear">',
        "initComplete": function() { highlightRowsWithDates(); },
        "drawCallback": function() { highlightRowsWithDates(); }
    });

    // Update photo counts for all rows
    const rows = document.querySelectorAll('tr[id^="row-"]');
    rows.forEach(row => {
        const id = row.id.replace('row-', '');
        updatePhotoCount(id);
    });
});
let currentDeliveryId = null;

function updateStatus(id, status) {
    if (status === 'Livré') {
        // Pour la livraison, ouvrir le modal de confirmation avec date
        currentDeliveryId = id;
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('deliveryDate').value = today;
        document.getElementById('deliveryModalBg').classList.add('active');
        return;
    }

    if (confirm(`Êtes-vous sûr de vouloir marquer ce colis comme ${status}?`)) {
        if (status === 'Annuler') {
            const postData = new FormData();
            postData.append('id', id);
            postData.append('bureau', 'web');
            postData.append('agent', 'web');
            postData.append('latitude', '0');
            postData.append('longitude', '0');
            fetch('https://leoexpress.tn/aapi9/createRetour.php', {
                method: 'POST', body: postData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const row = document.getElementById(`row-${id}`);
                    if (row) row.remove();
                    alert(`Le colis a été marqué comme ${status} avec succès!`);
                } else {
                    alert('Une erreur est survenue lors de la mise à jour du statut.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors de la création du retour.');
            });
        } else {
            fetch(`/reporte/update-status/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const row = document.getElementById(`row-${id}`);
                    if (row) row.remove();
                    alert(`Le colis a été marqué comme ${status} avec succès!`);
                } else {
                    alert('Une erreur est survenue lors de la mise à jour du statut.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue lors de la mise à jour du statut.');
            });
        }
    }
}

function closeDeliveryModal() {
    document.getElementById('deliveryModalBg').classList.remove('active');
    currentDeliveryId = null;
}

function confirmDelivery() {
    if (!currentDeliveryId) {
        alert('Erreur: ID du colis non trouvé');
        return;
    }

    const deliveryDate = document.getElementById('deliveryDate').value;
    if (!deliveryDate) {
        alert('Veuillez sélectionner une date de livraison');
        return;
    }

    // Store the ID before closing modal
    const deliveryId = currentDeliveryId;

    // Fermer le modal
    closeDeliveryModal();

    // Envoyer la requête de livraison
    fetch(`/reporte/mark-delivered/${deliveryId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            delivery_date: deliveryDate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const row = document.getElementById(`row-${deliveryId}`);
            if (row) row.remove();
            alert('Le colis a été marqué comme livré avec succès!');
        } else {
            alert('Une erreur est survenue lors de la mise à jour du statut.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Une erreur est survenue lors de la mise à jour du statut.');
    });
}

let currentRetourId = null;
let currentColisData = null;

async function showRetourModal(id) {
    currentRetourId = id;

    try {
        // Fetch colis details
        const response = await fetch(`/reporte/get-colis-details/${id}`);
        const data = await response.json();

        if (data.success) {
            currentColisData = data.colis;
            populateRetourModal(data.colis);
            document.getElementById('retourModalBg').classList.add('active');
        } else {
            alert('Erreur lors du chargement des détails du colis');
        }
    } catch (error) {
        console.error('Error fetching colis details:', error);
        alert('Erreur lors du chargement des détails du colis');
    }
}

function populateRetourModal(colis) {
    // Helper function to safely display values
    const safeValue = (value) => value || 'N/A';

    // Current colis details
    const currentDetails = `
        <div style="font-size: 0.9em; line-height: 1.6;">
            <strong>ID:</strong> ${colis.id}<br>
            <strong>Expéditeur:</strong> ${safeValue(colis.nom_exp)}<br>
            <strong>Tél Exp:</strong> ${safeValue(colis.num_tel_exp)}<br>
            <strong>Destinataire:</strong> ${safeValue(colis.nom_dest)}<br>
            <strong>Tél Dest:</strong> ${safeValue(colis.num_tel_dest)}<br>
            <strong>Bureau:</strong> ${safeValue(colis.bureau)}<br>
            <strong>Livreur:</strong> ${safeValue(colis.livreur_affecte)}<br>
            <strong>Total:</strong> ${safeValue(colis.total)} DT
        </div>
    `;

    // New retour details (reversed)
    const retourDetails = `
        <div style="font-size: 0.9em; line-height: 1.6;">
            <strong>Expéditeur:</strong> ${safeValue(colis.nom_dest)}<br>
            <strong>Tél Exp:</strong> ${safeValue(colis.num_tel_dest)}<br>
            <strong>Destinataire:</strong> ${safeValue(colis.nom_exp)}<br>
            <strong>Tél Dest:</strong> ${safeValue(colis.num_tel_exp)}<br>
            <strong>Bureau:</strong> ${safeValue(colis.livreur_affecte)}<br>
            <strong>Livreur:</strong> ${safeValue(colis.bureau)}<br>
            <strong>Total:</strong> ${safeValue(colis.total)} DT<br>
            <strong>Statut:</strong> <span style="color: #059669;">Nouveau</span>
        </div>
    `;

    document.getElementById('currentColisDetails').innerHTML = currentDetails;
    document.getElementById('newRetourDetails').innerHTML = retourDetails;
}

function closeRetourModal() {
    document.getElementById('retourModalBg').classList.remove('active');
    currentRetourId = null;
    currentColisData = null;
}

function confirmRetour() {
    if (!currentRetourId || !currentColisData) {
        alert('Erreur: Données du colis non trouvées');
        return;
    }

    // Store the ID before closing modal
    const retourId = currentRetourId;
    const colisData = currentColisData;

    // Close modal
    closeRetourModal();

    // Send retour request
    fetch(`/reporte/create-retour/${retourId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            colis_data: colisData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const row = document.getElementById(`row-${retourId}`);
            if (row) row.remove();
            alert(`Retour créé avec succès! Nouveau colis ID: ${data.new_colis_id}`);
        } else {
            alert('Une erreur est survenue lors de la création du retour: ' + (data.message || 'Erreur inconnue'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Une erreur est survenue lors de la création du retour.');
    });
}

// Add styles
const style = document.createElement('style');
style.textContent = `
    .location-list {
        padding: 10px;
    }
    .location-item {
        padding: 8px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        display: flex;
        flex-direction: column;
    }
    .location-item:hover {
        background: #f5f5f5;
    }
    .text-center {
        text-align: center;
        padding: 15px;
    }
    .text-danger {
        color: #e53935;
    }

    /* Custom marker styles */
    .custom-marker-icon {
        background: transparent;
    }
    .marker-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        background: #3a7bd5;
        color: white;
        border-radius: 50%;
        font-weight: bold;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        border: 2px solid white;
    }

    /* Improve location list appearance */
    #locationsList {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #eee;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    /* Add a polyline between markers */
    .leaflet-polyline {
        stroke: #3a7bd5;
        stroke-width: 3;
        stroke-opacity: 0.7;
    }

    /* Ensure map container has proper dimensions */
    #map {
        width: 100% !important;
        height: 400px !important;
        border-radius: 8px;
        border: 1px solid #ddd;
    }

    /* Map modal specific styles */
    #mapModalBg .leo-modal {
        max-width: 900px !important;
        width: 95vw !important;
    }

    /* Loading spinner for map */
    #mapSpinner {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        font-size: 1.5em;
        color: #666;
    }

    /* Delivery modal styles */
    #deliveryModalBg .leo-modal-body {
        padding: 20px;
    }

    #deliveryModalBg .form-control {
        width: 100%;
        padding: 10px;
        border: 2px solid #e0e7ff;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.2s ease;
    }

    #deliveryModalBg .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
    }

    #deliveryModalBg label {
        color: #374151;
        font-weight: 600;
        margin-bottom: 8px;
    }

    #deliveryModalBg p {
        color: #4b5563;
        line-height: 1.5;
    }

    /* Retour modal styles */
    #retourModalBg .leo-modal {
        max-width: 700px !important;
    }

    #retourModalBg .leo-modal-body {
        padding: 25px;
        max-height: 80vh;
        overflow-y: auto;
    }

    #retourModalBg h4 {
        margin: 0 0 10px 0;
        font-size: 1.1em;
        font-weight: 600;
    }

    #retourModalBg .grid-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    @media (max-width: 768px) {
        #retourModalBg .grid-container {
            grid-template-columns: 1fr;
        }

        #retourModalBg .leo-modal {
            max-width: 95vw !important;
        }
    }
`;
document.head.appendChild(style);
</script>