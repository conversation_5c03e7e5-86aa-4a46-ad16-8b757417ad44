<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Colis;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{    
    public function index()
    {
        $bureaux = DB::table('magasin_log')->distinct()->pluck('bureau');
        
        $latestLogs = DB::table('magasin_log')
            ->select('colisId', DB::raw('MAX(createdAt) as maxCreatedAt'))
            ->groupBy('colisId');
            
        $colis = DB::table('colis')
            ->where(function($query) {
                $query->where('colis.etat', 'Reporter')
                      ->orWhere(function($q) {
                          $q->where('colis.status', 'reporte');
                      });
            })
            ->where('date', '>=', '2025-05-01')
            ->leftJoin(DB::raw("({$latestLogs->toSql()}) as latest_logs"), function($join) {
                $join->on('colis.id', '=', 'latest_logs.colisId');
            })
            ->leftJoin('magasin_log', function($join) {
                $join->on('colis.id', '=', 'magasin_log.colisId')
                     ->on('magasin_log.createdAt', '=', 'latest_logs.maxCreatedAt');
            })
            ->select(
                'colis.id',
                'colis.num_tel_exp',
                'colis.nom_exp',
                'colis.num_tel_dest',
                'colis.nom_dest',
                'colis.destination',
                'colis.mode_payment',
                'colis.date',
                'colis.livreur_affecte as livreur',
                'colis.statusUpdatedAt',
                'magasin_log.agent',
                'magasin_log.bureau',
                'magasin_log.latitude',
                'magasin_log.longitude',
                'magasin_log.status',
                'magasin_log.createdAt as last_log_date',
                DB::raw('(SELECT COUNT(*) FROM magasin_log WHERE colisId = colis.id) as locations_count'),
                DB::raw('TIMESTAMPDIFF(HOUR, colis.date, NOW()) as hours_since_creation'),
                DB::raw('TIMESTAMPDIFF(HOUR, magasin_log.createdAt, NOW()) as hours_since_last_log')
            )
            ->orderBy('magasin_log.createdAt', 'desc')
            ->get();

        return view('report.index', compact('colis', 'bureaux'));
    }

    public function getReportedColis(Request $request)
    {
        $date = $request->input('date');
        $bureau = $request->input('bureau');
        $search = $request->input('search');

        $latestLogs = DB::table('magasin_log')
            ->select('colisId', DB::raw('MAX(createdAt) as maxCreatedAt'))
            ->groupBy('colisId');

        $query = DB::table('colis')
             ->where(function($query) {
                $query->where('colis.etat', 'Reporter')
                      ->orWhere(function($q) {
                          $q->where('colis.status', 'reporte');
                      });
            })
            ->leftJoin(DB::raw("({$latestLogs->toSql()}) as latest_logs"), function($join) {
                $join->on('colis.id', '=', 'latest_logs.colisId');
            })
            ->leftJoin('magasin_log', function($join) {
                $join->on('colis.id', '=', 'magasin_log.colisId')
                     ->on('magasin_log.createdAt', '=', 'latest_logs.maxCreatedAt');
            })
            ->select(
                'colis.id',
                'colis.num_tel_exp',
                'colis.nom_exp',
                'colis.num_tel_dest',
                'colis.nom_dest',
                'colis.destination',
                'colis.mode_payment',
                'colis.date',
                'colis.livreur_affecte as livreur',
                'colis.statusUpdatedAt',
                'magasin_log.agent',
                'magasin_log.bureau',
                'magasin_log.latitude',
                'magasin_log.longitude',
                'magasin_log.status',
                'magasin_log.createdAt as last_log_date',
                DB::raw('(SELECT COUNT(*) FROM magasin_log WHERE colisId = colis.id) as locations_count'),
                DB::raw('TIMESTAMPDIFF(HOUR, colis.date, NOW()) as hours_since_creation'),
                DB::raw('TIMESTAMPDIFF(HOUR, magasin_log.createdAt, NOW()) as hours_since_last_log')
            );

        if ($date) {
            $query->whereDate('magasin_log.createdAt', $date);
        }

        if ($bureau) {
            $query->where('magasin_log.bureau', $bureau);
        }

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('colis.nom_exp', 'LIKE', "%{$search}%")
                  ->orWhere('colis.nom_dest', 'LIKE', "%{$search}%");
            });
        }

        $query->orderBy('colis.id', 'desc');
        $colis = $query->get();

        return view('report.table', compact('colis'));
    }

    public function updateStatus(Request $request, $id)
    {
        try {
            $status = $request->input('status');

            if (!in_array($status, ['Livré', 'Annuler'])) {
                return response()->json(['success' => false, 'message' => 'Statut invalide']);
            }

            DB::table('colis')
                ->where('id', $id)
                ->update(['etat' => $status]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function markDelivered(Request $request, $id)
    {
        try {
            $deliveryDate = $request->input('delivery_date');

            if (!$deliveryDate) {
                return response()->json(['success' => false, 'message' => 'Date de livraison requise']);
            }

            // Valider le format de la date
            $date = \DateTime::createFromFormat('Y-m-d', $deliveryDate);
            if (!$date || $date->format('Y-m-d') !== $deliveryDate) {
                return response()->json(['success' => false, 'message' => 'Format de date invalide']);
            }

            // Mettre à jour le colis selon les spécifications :
            // status = null, etat = "Livré", date_livraison = date sélectionnée
            DB::table('colis')
                ->where('id', $id)
                ->update([
                    'status' => null,
                    'etat' => 'Livré',
                    'date_livraison' => $deliveryDate
                ]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function getColisDetails($id)
    {
        try {
            $colis = DB::table('colis')
                ->where('id', $id)
                ->first();

            if (!$colis) {
                return response()->json(['success' => false, 'message' => 'Colis non trouvé']);
            }

            return response()->json([
                'success' => true,
                'colis' => $colis
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function createRetour(Request $request, $id)
    {
        try {
            $colisData = $request->input('colis_data');

            if (!$colisData) {
                return response()->json(['success' => false, 'message' => 'Données du colis manquantes']);
            }

            DB::beginTransaction();

            // 1. Annuler le colis actuel
            DB::table('colis')
                ->where('id', $id)
                ->update([
                    'etat' => 'Annulé',
                    'status' => null
                ]);

            // 2. Créer le nouveau colis de retour avec les détails inversés
            $newColisId = DB::table('colis')->insertGetId([
                'date' => now()->format('Y-m-d'),
                'bureau' => $colisData['livreur_affecte'] ?? null,
                'utilisateur' => auth()->user()->name ?? 'web',
                'num_tel_exp' => $colisData['num_tel_dest'],
                'nom_exp' => $colisData['nom_dest'],
                'destination' => $colisData['bureau'] ?? null,
                'num_tel_dest' => $colisData['num_tel_exp'],
                'nom_dest' => $colisData['nom_exp'],
                'total' => $colisData['total'],
                'mode_payment' => $colisData['mode_payment'] ?? null,
                'montant_cr' => $colisData['montant_cr'] ?? null,
                'type_cr' => $colisData['type_cr'] ?? null,
                'observation' => 'Retour du colis #' . $id,
                'etat' => null,
                'sms' => 0,
                'livreur_affecte' => $colisData['bureau'] ?? null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'new_colis_id' => $newColisId,
                'message' => 'Retour créé avec succès'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function getLocations($id)
    {
        try {
            $locations = DB::table('magasin_log')
                ->where('colisId', $id)
                ->orderBy('createdAt', 'desc')
                ->get();

            return response()->json(['locations' => $locations]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}